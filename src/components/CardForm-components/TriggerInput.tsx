"use client";

import { TextStyle } from "@tiptap/extension-text-style";
import { Bold } from "@tiptap/extension-bold";
// import { Italic } from "@tiptap/extension-italic";
import { useDispatch, useSelector } from "react-redux";
import React, { useEffect, useState, useRef, useCallback, useMemo } from "react";
import {
  onTriggerTextFontSize,
  onTriggerEditorState,
  onTriggerText,
} from "@/store/formSlice";

import { StarterKit } from "@tiptap/starter-kit";
import { Underline } from "@tiptap/extension-underline";
import Highlight from "@tiptap/extension-highlight";
import { TextAlign } from "@tiptap/extension-text-align";
import { Color } from "@tiptap/extension-color";
import { BubbleMenu, EditorContent, useEditor, Editor } from "@tiptap/react";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { Button } from "@/components/ui/button";
import { CircleSlash2, ItalicIcon, Pipette } from "lucide-react";
import { storeState } from "@/store/store";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import { ColorPicker, ConfigProvider, theme } from "antd";
import type { Color as ColorAntd } from "antd/es/color-picker";
import { cn } from "@/lib/utils";
import { useDarkMode } from "@/hooks/useDarkMode";

const CustomParagraph = TextStyle.extend({
  renderHTML({ HTMLAttributes }) {
    if (HTMLAttributes.class === "orange-ability") {
      return [
        "span",
        { class: "orange-ability-container" },
        [
          "span",
          { class: "orange-ability-shadow" },
          ["span", { ...HTMLAttributes }, 0],
        ],
      ];
    } else if (HTMLAttributes.class === "black-ability") {
      return [
        "span",
        { class: "black-ability-container" },
        [
          "span",
          { class: "black-ability-shadow" },
          ["span", { ...HTMLAttributes }, 0],
        ],
      ];
    } else if (HTMLAttributes.class === "trigger-ability") {
      return [
        "span",
        { class: "trigger-ability-container" },
        [
          "span",
          { class: "trigger-ability-shadow" },
          ["span", { ...HTMLAttributes }, 0],
        ],
      ];
    } else {
      return ["span", { ...HTMLAttributes }, 0];
    }
  },
  addAttributes() {
    return {
      color: {
        // … and customize the HTML rendering.
        renderHTML: (attributes) => {
          const color = attributes.color;
          const classColor =
            color === "#2F77B3"
              ? "blue-ability"
              : color === "#d94880"
                ? "pink-ability"
                : color === "#DC8535"
                  ? "orange-ability"
                  : color === "#ba212f"
                    ? "red-ability"
                    : color === "#f8ed70"
                      ? "trigger-ability"
                      : color === "#FFFFFF"
                        ? "white-ability"
                        : color === "#000000"
                          ? "black-ability"
                          : "";
          return {
            class: classColor,
          };
        },
      },
    };
  },
});

const CustomBold = Bold.extend({
  name: "customBold",
  renderHTML({ HTMLAttributes }) {
    // Original:
    // return ['strong', HTMLAttributes, 0]
    return ["b", HTMLAttributes, 0];
  },
});

// const CustomItalic = Italic.extend({
//   name: "customItalic",
//   renderHTML({ HTMLAttributes }) {
//     const attributes = HTMLAttributes;
//     attributes.class = `font-one-piece-italic-bold text-[0.94em]`;
//     // Original:
//     // return ['strong', HTMLAttributes, 0]
//     return ["em", attributes, 0];
//   },
// });



// Memoized theme configuration to prevent recreation on every render
const createTriggerThemeConfig = (isDark: boolean) => ({
  components: { ColorPicker: {} },
  algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
  token: {
    colorBgElevated: isDark ? "#121212" : "#ffffff",
    colorText: isDark ? "#f9fafb" : "#000000",
    colorBorder: isDark ? "#121212" : "#d1d5db",
  },
});

// Memoized color presets to prevent recreation
const TRIGGER_COLOR_PRESETS = [
  {
    label: "Common Colors",
    colors: [
      "#25262b",
      "#868e96",
      "#fa5252",
      "#e64980",
      "#be4bdb",
      "#7950f2",
      "#4c6ef5",
      "#228be6",
      "#15aabf",
      "#12b886",
      "#40c057",
      "#82c91e",
      "#fab005",
      "#fd7e14",
    ],
  },
];

// Check EyeDropper support once
const IS_TRIGGER_EYEDROPPER_SUPPORTED = typeof window !== "undefined" && "EyeDropper" in window;

// Custom Color Picker Component for Trigger
const ColorPickerButton = React.memo(({ editor }: { editor: Editor | null }) => {
  const [currentColor, setCurrentColor] = useState("#000000");
  const debounceRef = useRef<NodeJS.Timeout | null>(null);
  const isDark = useDarkMode();

  // Memoize theme config to prevent recreation
  const themeConfig = useMemo(() => createTriggerThemeConfig(isDark), [isDark]);

  // Optimized color tracking - only update when color actually changes
  useEffect(() => {
    if (!editor) return;

    const updateColor = () => {
      const color = editor.getAttributes("textStyle").color || "#000000";
      setCurrentColor(prev => prev !== color ? color : prev);
    };

    // Update immediately
    updateColor();

    // Listen to editor updates instead of selection changes
    const handleUpdate = () => updateColor();
    editor.on("selectionUpdate", handleUpdate);
    editor.on("transaction", handleUpdate);

    return () => {
      editor.off("selectionUpdate", handleUpdate);
      editor.off("transaction", handleUpdate);
    };
  }, [editor]);

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  const handleColorChange = useCallback(
    (color: ColorAntd) => {
      const hexColor = color.toHexString();

      // Update UI immediately for responsive feel
      setCurrentColor(hexColor);

      // Debounce editor updates to prevent excessive re-renders
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }

      debounceRef.current = setTimeout(() => {
        if (editor && !editor.isDestroyed) {
          // Ensure we have a selection or create one
          const { from, to } = editor.state.selection;
          if (from === to) {
            // No selection, select all content
            editor
              .chain()
              .focus()
              .selectAll()
              .setMark("textStyle", { color: hexColor })
              .run();
          } else {
            // Apply color to selection
            editor
              .chain()
              .focus()
              .setMark("textStyle", { color: hexColor })
              .run();
          }
        }
      }, 100);
    },
    [editor],
  );

  // EyeDropper functionality
  const handleEyeDropper = useCallback(async () => {
    if (!IS_TRIGGER_EYEDROPPER_SUPPORTED) {
      return; // Browser doesn't support EyeDropper
    }

    try {
      // @ts-expect-error - EyeDropper is not in TypeScript types yet
      const eyeDropper = new window.EyeDropper();
      const result = await eyeDropper.open();

      if (result?.sRGBHex) {
        setCurrentColor(result.sRGBHex);

        // Apply to editor with debounce
        if (debounceRef.current) {
          clearTimeout(debounceRef.current);
        }

        debounceRef.current = setTimeout(() => {
          if (editor && !editor.isDestroyed) {
            // Ensure we have a selection or create one
            const { from, to } = editor.state.selection;
            if (from === to) {
              // No selection, select all content
              editor
                .chain()
                .focus()
                .selectAll()
                .setMark("textStyle", { color: result.sRGBHex })
                .run();
            } else {
              // Apply color to selection
              editor
                .chain()
                .focus()
                .setMark("textStyle", { color: result.sRGBHex })
                .run();
            }
          }
        }, 100);
      }
    } catch (error) {
      // User cancelled or error occurred
      console.log("EyeDropper cancelled or failed:", error);
    }
  }, [editor]);

  return (
    <ConfigProvider theme={themeConfig}>
      <div className="divide-input flex items-center divide-x rounded-l-sm rounded-r-sm border-1">
        <ColorPicker
          value={currentColor}
          onChange={handleColorChange}
          size="middle"
          showText={false}
          placement="bottomLeft"
          className={
            "bg-background! hover:bg-accent! hover:text-accent-foreground! rounded-l-sm! rounded-r-none! shadow-xs! hover:border-transparent!"
          }
          arrow={false}
          presets={TRIGGER_COLOR_PRESETS}
        />
        {IS_TRIGGER_EYEDROPPER_SUPPORTED && (
          <Button
            variant="editor"
            size="editor"
            className="h-8 w-8 rounded-l-none rounded-r-none border-l-1 p-0"
            onClick={handleEyeDropper}
            title="Pick color from screen"
          >
            <Pipette className="h-4 w-4" />
          </Button>
        )}
        <Button
          variant="editor"
          size="editor"
          onClick={() => editor?.chain().focus().unsetAllMarks().run()}
          className={"rounded-l-none rounded-r-sm"}
        >
          <CircleSlash2 className={"w-3.5! rotate-90"} />
        </Button>
      </div>
    </ConfigProvider>
  );
});

ColorPickerButton.displayName = "TriggerColorPickerButton";

export default function TriggerInput() {
  const editorState = useGetStoreState("triggerEditorState");
  const dispatch = useDispatch();

  // const notStageRoute = route.Characters !== "Stage";
  // const notEventRoute = route.Characters !== "Event";
  // const notDonRoute = route.Characters !== "Don";
  const editor = useEditor({
    content: editorState,
    immediatelyRender: false,
    extensions: [
      StarterKit,
      Underline,
      Highlight,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
      Color.configure({
        types: ["textStyle"],
      }),
      CustomParagraph,
      CustomBold,
      // CustomItalic,
    ],
    editorProps: {
      attributes: {
        class: "ability-input",
      },
    },
    onUpdate({ editor }) {
      dispatch(onTriggerText(editor.getHTML()));
    },
    onDestroy() {
      dispatch(onTriggerEditorState(editor?.getJSON()));
    },
    onBlur() {
      dispatch(onTriggerEditorState(editor?.getJSON()));
    },
    onCreate({ editor }) {
      editor.commands.setContent(editorState);
    },
  });
  return (
    <>
      <div className="flex flex-col">
        <label className="text-sm">Trigger</label>
        <p className="text-xs text-[#868e96]">Card trigger</p>
        <div className="bg-background relative mt-1 max-w-full rounded-md border lg:max-w-[697.42px] dark:bg-neutral-800">
          {/* Toolbar */}
          <div className="flex flex-row flex-wrap gap-2 gap-y-2 rounded-t-md border-b p-2 dark:bg-neutral-900/70">
            <div className="flex gap-1">
              <div className="divide-input border-input flex divide-x rounded-sm border">
                <TextIncreaseDecrease
                  by={-0.5}
                  label="Decrease font size"
                  className={"rounded-l-sm rounded-r-none"}
                >
                  {"<"}
                </TextIncreaseDecrease>
                <TextSize />
                <TextIncreaseDecrease
                  by={0.5}
                  label="Increase font size"
                  className={"rounded-l-none rounded-r-sm"}
                >
                  {">"}
                </TextIncreaseDecrease>
              </div>
            </div>

            <ColorPickerButton editor={editor} />
          </div>

          {editor && (
            <BubbleMenu
              editor={editor}
              tippyOptions={{
                offset: [80, -90],
                zIndex: 10,
                interactive: true,
                appendTo: () => document.body,
              }}
              className="bg-popover divide-input flex items-center divide-x rounded-md border shadow-md"
            >
              <Tooltip disableHoverableContent>
                <TooltipTrigger asChild>
                  <Button
                    variant="editor"
                    size="editor"
                    onClick={() =>
                      editor
                        .chain()
                        .focus()
                        .setMark("textStyle", { color: "#2F77B3" })
                        .run()
                    }
                    onMouseDown={(e) => e.preventDefault()}
                    className="bg-popover h-[31px] w-15 rounded-l-md rounded-r-none px-2! py-2"
                  >
                    <div
                      className={
                        "border-input h-full w-full rounded-full border bg-[#2F77B3]"
                      }
                    ></div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent
                  className="border-0 bg-transparent p-0"
                  arrowPadding={0}
                >
                  <p className={"blue-ability-tooltip z-10 text-[16px]!"}>
                    OnPlay
                  </p>
                </TooltipContent>
              </Tooltip>

              <Tooltip disableHoverableContent>
                <TooltipTrigger asChild>
                  <Button
                    variant="editor"
                    size="editor"
                    onClick={() =>
                      editor
                        .chain()
                        .focus()
                        .setMark("textStyle", { color: "#d94880" })
                        .run()
                    }
                    onMouseDown={(e) => e.preventDefault()}
                    className="bg-popover h-[31px] w-15 rounded-none px-2! py-2"
                  >
                    <div
                      className={
                        "border-input h-full w-full rounded-full border bg-[#d94880]"
                      }
                    ></div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="border-0 bg-transparent p-0 shadow-none">
                  <p className={"pink-ability-tooltip z-10 text-[16px]!"}>
                    Once Per Turn
                  </p>
                </TooltipContent>
              </Tooltip>

              <Tooltip disableHoverableContent>
                <TooltipTrigger asChild>
                  <Button
                    variant="editor"
                    size="editor"
                    onClick={() =>
                      editor
                        .chain()
                        .focus()
                        .setMark("textStyle", { color: "#DC8535" })
                        .run()
                    }
                    onMouseDown={(e) => e.preventDefault()}
                    className="bg-popover h-[31px] w-15 rounded-none px-2! py-2"
                  >
                    <div
                      className={
                        "border-input h-full w-full rounded-full border bg-[#DC8535]"
                      }
                    ></div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="border-0 bg-transparent p-0 shadow-none">
                  <span
                    className={
                      "orange-ability-container top-[0.2rem] z-10 text-[20px]!"
                    }
                  >
                    <span className={"orange-ability-shadow"}>
                      <span className={"orange-ability"}>Blocker</span>
                    </span>
                  </span>
                </TooltipContent>
              </Tooltip>

              <Tooltip disableHoverableContent>
                <TooltipTrigger asChild>
                  <Button
                    variant="editor"
                    size="editor"
                    onClick={() =>
                      editor
                        .chain()
                        .focus()
                        .setMark("textStyle", { color: "#ba212f" })
                        .run()
                    }
                    onMouseDown={(e) => e.preventDefault()}
                    className="bg-popover h-[31px] w-15 rounded-none px-2! py-2"
                  >
                    <div
                      className={
                        "border-input h-full w-full rounded-full border bg-[#ba212f]"
                      }
                    ></div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="border-0 bg-transparent p-0 shadow-none">
                  <p className={"counter-ability-tooltip z-10 text-[16px]!"}>
                    Counter
                  </p>
                </TooltipContent>
              </Tooltip>

              <Tooltip disableHoverableContent>
                <TooltipTrigger asChild>
                  <Button
                    variant="editor"
                    size="editor"
                    onClick={() =>
                      editor
                        .chain()
                        .focus()
                        .setMark("textStyle", { color: "#f8ed70" })
                        .run()
                    }
                    onMouseDown={(e) => e.preventDefault()}
                    className="bg-popover h-[31px] w-15 rounded-none px-2! py-2"
                  >
                    <div
                      className={
                        "border-input h-full w-full rounded-full border bg-[#f8ed70]"
                      }
                    ></div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="border-0 bg-transparent p-0 shadow-none">
                  <p className={"trigger-ability-tooltip z-10 text-[10px]!"}>
                    Trigger
                  </p>
                </TooltipContent>
              </Tooltip>

              <Tooltip disableHoverableContent>
                <TooltipTrigger asChild>
                  <Button
                    variant="editor"
                    size="editor"
                    onClick={() =>
                      editor
                        .chain()
                        .focus()
                        .setMark("textStyle", { color: "#FFFFFF" })
                        .run()
                    }
                    onMouseDown={(e) => e.preventDefault()}
                    className="bg-popover h-[31px] w-15 rounded-none px-2! py-2"
                  >
                    <div
                      className={
                        "border-input h-full w-full rounded-full border bg-[#FFFFFF]"
                      }
                    ></div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="number-ability-tooltip z-10 border-0 bg-transparent p-0 shadow-none">
                  <p className={"number-ability-tooltip z-10 text-[16px]!"}>
                    1
                  </p>
                </TooltipContent>
              </Tooltip>
              <Button
                variant="editor"
                size="editor"
                onClick={() => editor.chain().focus().toggleBold().run()}
                onMouseDown={(e) => e.preventDefault()}
                className={`bg-popover h-[31px] w-15 rounded-r-none ${
                  editor.isActive("customBold")
                    ? "bg-neutral-800 text-neutral-50"
                    : ""
                } `}
              >
                <strong>B</strong>
              </Button>
              <Button
                variant="editor"
                size="editor"
                onClick={() => editor.chain().focus().toggleItalic().run()}
                onMouseDown={(e) => e.preventDefault()}
                className={`bg-popover h-[31px] w-15 rounded-none px-2! py-2 text-lg! ${
                  // editor.isActive("customItalic")
                  editor.isActive("italic")
                    ? "bg-neutral-800 text-neutral-50"
                    : "bg-transparent"
                }`}
              >
                <ItalicIcon />
              </Button>
              <Button
                variant="editor"
                size="editor"
                onClick={() => editor.chain().focus().toggleUnderline().run()}
                onMouseDown={(e) => e.preventDefault()}
                className={`bg-popover h-[31px] w-15 rounded-r-none ${
                  editor.isActive("underline")
                    ? "bg-neutral-800 text-neutral-50"
                    : ""
                } `}
              >
                <u>U</u>
              </Button>

              <Button
                variant="editor"
                size="editor"
                onClick={() => editor.chain().focus().toggleStrike().run()}
                onMouseDown={(e) => e.preventDefault()}
                className={`bg-popover h-[31px] w-15 rounded-r-none ${
                  editor.isActive("strike")
                    ? "bg-neutral-800 text-neutral-50"
                    : ""
                } `}
              >
                <s>S</s>
              </Button>
              <Button
                variant="editor"
                size="editor"
                onClick={() => editor.chain().focus().unsetColor().run()}
                onMouseDown={(e) => e.preventDefault()}
                className={
                  "bg-popover h-[31px] w-15 rounded-l-none rounded-r-md px-[0.485rem]! py-2"
                }
              >
                <CircleSlash2 className={"h-3.5! rotate-90"} />
              </Button>
            </BubbleMenu>
          )}

          {/* Editor Content */}
          <EditorContent
            editor={editor}
            className="font-roboto min-h-20 overflow-clip"
          />

          <DefaultStateSetter editor={editor} />
        </div>
      </div>
    </>
  );
}

function DefaultStateSetter({ editor }: { editor: Editor | null }) {
  // Get the default state for the editor from some store
  const editorDefaultState = useGetStoreState("triggerEditorState");

  useEffect(() => {
    // Only set content if the editor exists
    if (editor && editorDefaultState) {
      editor.commands.setContent(editorDefaultState);
    }
  }, [editor, editorDefaultState]);

  // This component does not render anything
  return null;
}

function TextIncreaseDecrease({
  children,
  by,
  label,
  className,
}: {
  children: React.ReactNode;
  by: number;
  label: string;
  className?: string;
}) {
  const dispatch = useDispatch();
  const triggerTextFontSize = useGetStoreState("triggerTextFontSize");
  return (
    <Button
      variant="editor"
      size="editor"
      onClick={() => {
        dispatch(onTriggerTextFontSize(by));
      }}
      aria-label={label}
      title={label}
      className={cn("px-2", className)}
      disabled={triggerTextFontSize === 1 && label === "Decrease font size"}
    >
      {children}
    </Button>
  );
}

function TextSize() {
  const size = useSelector(
    (state: storeState) => state.mainFormSlice.triggerTextFontSize,
  );
  const dispatch = useDispatch();
  return (
    <Tooltip disableHoverableContent>
      <TooltipTrigger asChild>
        <Button
          variant="editor"
          size="editor"
          onClick={() => {
            dispatch(onTriggerTextFontSize("default"));
          }}
          aria-label="Reset font size"
          className="min-w-[4rem] rounded-l-none rounded-r-none"
        >
          <div className="flex content-center justify-center text-center">
            <p>{size + "px"}</p>
          </div>
        </Button>
      </TooltipTrigger>
      <TooltipContent>Reset font size</TooltipContent>
    </Tooltip>
  );
}
