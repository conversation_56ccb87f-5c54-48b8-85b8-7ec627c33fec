import { useRef, useCallback, useEffect } from 'react';
import { Dispatch } from '@reduxjs/toolkit';

/**
 * Custom hook for debouncing dispatch calls
 * @param dispatch - Redux dispatch function
 * @param delay - Debounce delay in milliseconds
 * @returns Debounced dispatch function
 */
export function useDebouncedDispatch(dispatch: Dispatch, delay: number = 300) {
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  const debouncedDispatch = useCallback(
    (action: any) => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }

      debounceRef.current = setTimeout(() => {
        dispatch(action);
      }, delay);
    },
    [dispatch, delay],
  );

  return debouncedDispatch;
}
